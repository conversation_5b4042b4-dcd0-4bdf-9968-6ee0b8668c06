# 로또번호 추천 시스템

Python으로 구현된 로또번호 추천 시스템입니다. Flask 웹 애플리케이션과 명령줄 도구를 모두 제공합니다.

## 기능

### 🎰 로또번호 생성 방식
- **랜덤 생성**: 완전 무작위 번호 생성
- **가중치 생성**: 특정 구간에 가중치를 적용한 번호 생성
- **날짜 기반**: 오늘 날짜를 시드로 사용한 일관된 번호 생성
- **패턴 기반**: 연속번호, 끝자리 등을 고려한 번호 생성

### 📊 분석 기능
- 번호 합계 및 평균
- 홀수/짝수 개수
- 저번호(1-22)/고번호(23-45) 분포
- 연속번호 쌍 검출
- 끝자리 분석

## 사용법

### 1. Flask 웹 애플리케이션

```bash
python app.py
```

웹 브라우저에서 `http://localhost:5000`으로 접속하면 다음 기능을 사용할 수 있습니다:

- **메인 페이지**: 다양한 방식의 로또번호 추천
- **API 엔드포인트**:
  - `GET /api/lotto` - 단일 로또번호 세트
  - `GET /api/lotto/multiple/<count>` - 여러 세트 (최대 10개)
  - `GET /api/lotto/lucky` - 오늘의 행운 번호

### 2. 명령줄 도구

```bash
# 기본 사용법 (랜덤 1세트)
python lotto_recommender.py

# 5세트 생성
python lotto_recommender.py --count 5

# 가중치 방식으로 3세트 생성
python lotto_recommender.py --count 3 --method weighted

# 분석 정보 포함
python lotto_recommender.py --analyze

# JSON 형식으로 출력
python lotto_recommender.py --json

# 모든 옵션 조합
python lotto_recommender.py --count 3 --method pattern --analyze --json
```

#### 명령줄 옵션
- `--count, -c`: 생성할 세트 수 (기본값: 1)
- `--method, -m`: 생성 방법 (random, weighted, date, pattern)
- `--analyze, -a`: 번호 분석 정보 표시
- `--json, -j`: JSON 형식으로 출력

## 예시 출력

### 웹 인터페이스
웹 페이지에서는 다음과 같은 정보를 제공합니다:
- 오늘의 추천 번호 (시각적인 로또볼 형태)
- 날짜 기반 행운 번호
- 추가 추천 번호 세트들
- 생성 시간

### 명령줄 출력
```
🎰 로또번호 추천 (random 방식)
========================================
추천 번호: 7 - 15 - 23 - 31 - 38 - 42
생성 시간: 2024-01-15 14:30:25

📊 번호 분석
--------------------
합계: 156
평균: 26.0
홀수: 3개, 짝수: 3개
저번호(1-22): 2개, 고번호(23-45): 4개
끝자리: [7, 5, 3, 1, 8, 2]
```

## 파일 구조

```
Flask/
├── app.py                 # Flask 웹 애플리케이션
├── lotto_recommender.py   # 명령줄 로또번호 추천 도구
├── templates/
│   └── index.html        # 웹 인터페이스 템플릿
└── README.md             # 이 파일
```

## 요구사항

- Python 3.6+
- Flask (웹 애플리케이션용)

```bash
pip install flask
```

## 특징

1. **다양한 생성 방식**: 완전 랜덤부터 패턴 기반까지 다양한 방식 지원
2. **웹과 CLI 모두 지원**: 웹 인터페이스와 명령줄 도구 모두 제공
3. **분석 기능**: 생성된 번호의 통계적 특성 분석
4. **API 지원**: RESTful API로 다른 애플리케이션과 연동 가능
5. **시각적 인터페이스**: 로또볼 형태의 직관적인 웹 인터페이스

## 주의사항

이 프로그램은 교육 및 재미 목적으로 만들어졌습니다. 로또는 순전히 운에 의한 게임이며, 어떤 알고리즘도 당첨을 보장할 수 없습니다. 책임감 있는 게임을 즐기시기 바랍니다.

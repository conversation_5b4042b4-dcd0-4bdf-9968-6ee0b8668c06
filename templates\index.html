<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>로또번호 추천 시스템</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .lotto-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #3498db;
            border-radius: 8px;
            background-color: #ecf0f1;
        }
        .lotto-numbers {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
            margin: 15px 0;
        }
        .lotto-ball {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
            color: white;
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        .lucky-ball {
            background: linear-gradient(45deg, #f39c12, #e67e22);
        }
        .section-title {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 20px;
        }
        .multiple-sets {
            display: grid;
            gap: 15px;
            margin-top: 20px;
        }
        .set-item {
            padding: 15px;
            background-color: white;
            border-radius: 5px;
            border-left: 4px solid #9b59b6;
        }
        .timestamp {
            text-align: center;
            color: #7f8c8d;
            font-size: 14px;
            margin-top: 20px;
        }
        .refresh-btn {
            display: block;
            margin: 20px auto;
            padding: 12px 24px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            text-align: center;
        }
        .refresh-btn:hover {
            background-color: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎰 로또번호 추천 시스템</h1>
        <p style="text-align: center; color: #7f8c8d;">안녕하세요, {{data.name}}님! 행운의 번호를 추천해드립니다.</p>

        <!-- 추천 번호 -->
        <div class="lotto-section">
            <h3 class="section-title">🎯 오늘의 추천 번호</h3>
            <div class="lotto-numbers">
                {% for number in data.lotto %}
                    <div class="lotto-ball">{{ number }}</div>
                {% endfor %}
            </div>
        </div>

        <!-- 행운의 번호 -->
        <div class="lotto-section">
            <h3 class="section-title">🍀 오늘의 행운 번호</h3>
            <p style="color: #7f8c8d; font-size: 14px;">날짜 기반으로 생성된 특별한 번호입니다.</p>
            <div class="lotto-numbers">
                {% for number in data.lucky_numbers %}
                    <div class="lotto-ball lucky-ball">{{ number }}</div>
                {% endfor %}
            </div>
        </div>

        <!-- 여러 세트 -->
        <div class="lotto-section">
            <h3 class="section-title">🎲 추가 추천 번호</h3>
            <div class="multiple-sets">
                {% for set in data.multiple_sets %}
                    <div class="set-item">
                        <strong>세트 {{ set.set_number }}:</strong>
                        <div class="lotto-numbers" style="justify-content: flex-start; margin-top: 10px;">
                            {% for number in set.numbers %}
                                <div class="lotto-ball" style="width: 35px; height: 35px; font-size: 14px;">{{ number }}</div>
                            {% endfor %}
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>

        <a href="/" class="refresh-btn">🔄 새로운 번호 생성</a>

        <div class="timestamp">
            생성 시간: {{ data.generation_time }}
        </div>
    </div>
</body>
</html>
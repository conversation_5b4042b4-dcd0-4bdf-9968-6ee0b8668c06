from flask import Flask, render_template, jsonify
import random
from datetime import datetime

app = Flask(__name__)

def generate_lotto_numbers():
    """1부터 45까지의 숫자 중에서 중복되지 않는 6개의 번호를 생성"""
    numbers = random.sample(range(1, 46), 6)
    return sorted(numbers)

def generate_multiple_lotto_sets(count=5):
    """여러 세트의 로또번호를 생성"""
    lotto_sets = []
    for i in range(count):
        numbers = generate_lotto_numbers()
        lotto_sets.append({
            'set_number': i + 1,
            'numbers': numbers
        })
    return lotto_sets

def get_lucky_numbers_by_date():
    """오늘 날짜를 기반으로 한 행운의 번호 생성"""
    today = datetime.now()
    # 날짜를 시드로 사용하여 일관된 결과 생성
    random.seed(today.strftime("%Y%m%d"))
    lucky_numbers = generate_lotto_numbers()
    # 시드 초기화
    random.seed()
    return lucky_numbers

@app.route('/')
def home():
    name = '김태민'
    # 추천 로또번호 생성
    recommended_numbers = generate_lotto_numbers()
    # 오늘의 행운 번호
    lucky_numbers = get_lucky_numbers_by_date()
    # 여러 세트 생성
    multiple_sets = generate_multiple_lotto_sets(3)

    context = {
        "name": name,
        "lotto": recommended_numbers,
        "lucky_numbers": lucky_numbers,
        "multiple_sets": multiple_sets,
        "generation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    return render_template('index.html', data=context)
@app.route('/mypage')
def mypage():
    return 'This is MyPage!'

@app.route('/api/lotto')
def api_lotto():
    """로또번호 API 엔드포인트"""
    return jsonify({
        'numbers': generate_lotto_numbers(),
        'generated_at': datetime.now().isoformat()
    })

@app.route('/api/lotto/multiple/<int:count>')
def api_multiple_lotto(count):
    """여러 세트의 로또번호 API 엔드포인트"""
    if count > 10:  # 최대 10세트로 제한
        count = 10
    if count < 1:
        count = 1

    return jsonify({
        'sets': generate_multiple_lotto_sets(count),
        'generated_at': datetime.now().isoformat()
    })

@app.route('/api/lotto/lucky')
def api_lucky_lotto():
    """오늘의 행운 번호 API 엔드포인트"""
    return jsonify({
        'lucky_numbers': get_lucky_numbers_by_date(),
        'date': datetime.now().strftime("%Y-%m-%d"),
        'generated_at': datetime.now().isoformat()
    })

if __name__ == '__main__':  
    app.run(debug=True)